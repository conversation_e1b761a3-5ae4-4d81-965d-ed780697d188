package com.joyadata.dsc.datasource.model.datasoure;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据源授权
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JoyadataTable(name = "dsc_datasource_authorization", label = "dsc_datasource_authorization", comment = "数据源授权表")
public class DatasourceAuthorization extends BaseBean {

    @JoyadataColumn(label = "数据源类型id")
    private String datasourceInfoId;

    @JoyadataColumn(label = "授权主体的类型", comment = "1:项目 2:部门 3:用户 4:角色 5:用户组")
    private Integer principalType;

    @JoyadataColumn(label = "授权主体的ID")
    private String principalId;

    @JoyadataColumn(label = "授权范围类型", comment = "0:ALL 1:SPECIFIC")
    private Integer scopeType;

    @JoyadataColumn(label = "是否可查看", comment = "0:否 1:是")
    private Integer canView;

    @JoyadataColumn(label = "是否可维护", comment = "0:否 1:是")
    private Integer canMaintain;

    @JoyadataColumn(label = "是否可删除", comment = "0:否 1:是")
    private Integer canDelete;

    public static class ScopeType {

        /**
         * 全部
         */
        public static final Integer ALL = 0;

        /**
         * 部分指定
         */
        public static final Integer SPECIFIC = 1;
    }

    /**
     * 授权类型
     */
    public static class PrincipalType {

        /**
         *  项目
         */
        public static final Integer PROJECT = 1;

        /**
         *  部门
         */
        public static final Integer DEPARTMENT = 2;

        /**
         *  用户
         */
        public static final Integer USER = 3;

        /**
         *  角色
         */
        public static final Integer ROLE = 4;

        /**
         *  用户组
         */
        public static final Integer USER_GROUP = 5;
    }
}
