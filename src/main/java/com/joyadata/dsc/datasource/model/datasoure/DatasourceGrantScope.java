package com.joyadata.dsc.datasource.model.datasoure;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 授权范围表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JoyadataTable(name = "dsc_datasource_grant_scope", label = "dsc_datasource_grant_scope", comment = "数据源授权范围表")
public class DatasourceGrantScope extends BaseBean {

    @JoyadataColumn(label = "数据源授权id")
    private String datasourceAuthorizationId;

    @JoyadataColumn(label = "数据库名称")
    private String databaseName;

    @JoyadataColumn(label = "是否可查看", comment = "0:否 1:是")
    private Integer canView;

    @JoyadataColumn(label = "是否可维护", comment = "0:否 1:是")
    private Integer canMaintain;

    @JoyadataColumn(label = "是否可删除", comment = "0:否 1:是")
    private Integer canDelete;
}
